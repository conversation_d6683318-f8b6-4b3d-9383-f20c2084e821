<?php
/**
 * 还原JavaScript代码功能的PHP实现
 * 
 * 原始JS代码分析：
 * 1. pK[UA] = new window[FA](Fg, LA) - 创建一个加密对象
 * 2. pK[xm] = function(r, t) - 定义一个加密函数
 * 3. 该函数检查数据类型，遍历对象属性，拼接字符串，添加时间戳，生成签名
 */

class CryptoHelper {
    private $cryptoInstance;
    
    public function __construct($key1, $key2) {
        // 模拟 new window[FA](Fg, LA)
        $this->cryptoInstance = $this->createCryptoInstance($key1, $key2);
    }
    
    private function createCryptoInstance($key1, $key2) {
        // 这里需要根据实际的加密算法来实现
        // 从代码看可能是某种HMAC或类似的加密方式
        return [
            'key1' => $key1,
            'key2' => $key2
        ];
    }
    
    /**
     * 还原 pK[xm] 函数的功能
     * 
     * @param mixed $data 要处理的数据
     * @param string $key 加密密钥
     * @return string 处理后的JSON字符串
     */
    public function processData($data, $key) {
        // 检查数据类型 - 对应 if (go != i(r))
        if (!is_object($data) && !is_array($data)) {
            return json_encode($data);
        }
        
        $concatenatedString = "";
        
        // 遍历对象属性 - 对应 for (var u in n = "", Object[Ig](r)[bE]())
        if (is_array($data) || is_object($data)) {
            foreach ($data as $property => $value) {
                $valueType = gettype($value);
                
                // 检查值类型 - 对应 pr != s && yr != s && VA != s
                // 这里假设 pr, yr, VA 分别对应 number, string, boolean 类型
                if ($valueType === 'integer' || $valueType === 'double' || 
                    $valueType === 'string' || $valueType === 'boolean') {
                    $concatenatedString .= $value;
                }
            }
        }
        
        // 添加时间戳 - 对应 n += e = Date[jA]()
        $timestamp = $this->getCurrentTimestamp();
        $concatenatedString .= $timestamp;
        var_dump($concatenatedString);
        exit;
        
        // 设置时间戳到数据中 - 对应 r[GA] = e
        if (is_array($data)) {
            $data['_time_'] = $timestamp;
        } elseif (is_object($data)) {
            $data->_time_ = $timestamp;
        }
        
        // 生成签名 - 对应 r[-1] = pK[UA][tn](t, window[ZA][Yr][Jr][lo](n))
        $signature = $this->generateSignature($key, $concatenatedString);
        
        // 设置签名到数据中 - 对应 r[-1] = ...
        if (is_array($data)) {
            $data[-1] = $signature;
        } elseif (is_object($data)) {
            $data->{-1} = $signature;
        }
        
        // 返回处理后的数据 - 对应 pK[$A](JSON[rn](r))
        return $this->finalProcess(json_encode($data));
    }
    
    /**
     * 获取当前时间戳
     * 对应 Date[jA]()
     */
    private function getCurrentTimestamp() {
        return time() * 1000; // JavaScript时间戳是毫秒
    }
    
    /**
     * 生成签名
     * 对应 pK[UA][tn](t, window[ZA][Yr][Jr][lo](n))
     */
    private function generateSignature($key, $data) {
        // 这里需要根据实际的加密算法实现
        // 从代码结构看，可能是HMAC-SHA256或类似算法
        return hash_hmac('sha256', $data, $key);
    }
    
    /**
     * 最终处理函数
     * 对应 pK[$A](JSON[rn](r))
     */
    private function finalProcess($jsonString) {
        // 这里可能有额外的编码或处理步骤
        // 根据原代码，可能涉及base64编码或其他转换
        return $jsonString;
    }
}

// 使用示例
function restoreJSFunction($data, $key, $cryptoKey1 = 'default_key1', $cryptoKey2 = 'default_key2') {
    $crypto = new CryptoHelper($cryptoKey1, $cryptoKey2);
    return $crypto->processData($data, $key);
}

// 从 r.json 文件读取测试数据
function loadTestDataFromJson($filePath = 'r.json') {
    if (!file_exists($filePath)) {
        throw new Exception("文件 {$filePath} 不存在");
    }

    $jsonContent = file_get_contents($filePath);
    if ($jsonContent === false) {
        throw new Exception("无法读取文件 {$filePath}");
    }

    $testData = json_decode($jsonContent, true);
    if (json_last_error() !== JSON_ERROR_NONE) {
        throw new Exception("JSON解析错误: " . json_last_error_msg());
    }

    return $testData;
}

// 测试用例
try {
    $testData = loadTestDataFromJson('r.json');
    echo "成功从 r.json 读取数据，数据项数量: " . count($testData) . "\n";

    $result = restoreJSFunction($testData, 'test_key');
    echo "处理结果: " . $result . "\n";

    // 显示部分原始数据用于验证
    echo "\n原始数据示例:\n";
    $sampleKeys = array_slice(array_keys($testData), 0, 5);
    foreach ($sampleKeys as $key) {
        $value = $testData[$key];
        if (is_array($value) || is_object($value)) {
            echo "  {$key}: " . json_encode($value) . "\n";
        } else {
            echo "  {$key}: {$value}\n";
        }
    }

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";

    // 如果读取失败，使用默认测试数据
    echo "使用默认测试数据...\n";
    $testData = [
        'user_id' => 12345,
        'action' => 'login',
        'timestamp' => time(),
        'extra' => true
    ];

    $result = restoreJSFunction($testData, 'test_key');
    echo "处理结果: " . $result . "\n";
}

// 如果需要验证签名的功能
class SignatureValidator {
    private $cryptoHelper;
    
    public function __construct($key1, $key2) {
        $this->cryptoHelper = new CryptoHelper($key1, $key2);
    }
    
    /**
     * 验证数据签名
     * 类似原JS代码中可能存在的验证逻辑
     */
    public function validateSignature($data, $key) {
        if (!is_array($data) && !is_object($data)) {
            return true; // 非对象数据直接通过
        }
        
        $dataArray = is_object($data) ? (array)$data : $data;
        
        if (!isset($dataArray[-1]) || !isset($dataArray['_time_'])) {
            return true; // 没有签名信息，直接通过
        }
        
        $signature = $dataArray[-1];
        $timestamp = $dataArray['_time_'];
        
        // 移除签名和时间戳，重新计算
        unset($dataArray[-1]);
        unset($dataArray['_time_']);
        
        $concatenatedString = "";
        foreach ($dataArray as $value) {
            $valueType = gettype($value);
            if ($valueType === 'integer' || $valueType === 'double' || 
                $valueType === 'string' || $valueType === 'boolean') {
                $concatenatedString .= $value;
            }
        }
        $concatenatedString .= $timestamp;
        
        $expectedSignature = hash_hmac('sha256', $concatenatedString, $key);
        
        return hash_equals($expectedSignature, $signature);
    }
}

?>
